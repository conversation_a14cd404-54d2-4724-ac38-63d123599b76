

export enum ReceivedStatus {
  /**
   * 已取消
   */
  CANCELLED = -2,
  /**
   * 审核不通过
   */
  ABORTED = -1,
  /**
   * 草稿
   */
  DRAFT = 0,
  /**
  /**
   * 待审核
   */
  PENDING = 1,
  /**
   * 待支付
   */
  UNPAYMENT = 2,
  /**
   * 已收款
   */
  PAYMENT = 3,
}

export enum ReceivedType {
  /**
   * 收入
   */
  INCOME = 1,
  /**
   * 支出
   */
  EXPENSE = 2,
}


export interface FinReceivedRo {
  /**
   * 调整金额，单位：分
   */
  adjustAmount?: number;
  /**
   * 调整金额，单位：元
   */
  adjustAmountYuan?: number;
  /**
   * 调整原因
   */
  adjustReason?: string;
  /**
   * 调整类型1：无，2：收款抹零，3：收款优惠'
   */
  adjustType?: number;
  /**
   * 转预收金额
   */
  advanceAmount?: number;
  /**
   * 转预收金额（元）
   */
  advanceAmountYuan?: number;
  /**
   * 审核人
   */
  auditor?: string;
  /**
   * 审核时间
   */
  auditTime?: string;
  /**
   * 收款时间
   */
  businessTime?: string;
  /**
   * 客户ID
   */
  buyerId?: string;
  /**
   * 客户名称
   */
  buyerName?: string;
  /**
   * 制单人
   */
  createPerson?: string;
  /**
   * 币种
   */
  currency?: string;
  currencySymbol?: string;
  /**
   * 收款类型，1：收入，2：支出
   */
  ledgerType?: ReceivedType;
  /**
   * 本货币汇率损益
   */
  lossAmount?: number;
  /**
   * 本货币汇率损益（元）
   */
  lossAmountYuan?: number;
  /**
   * 汇率
   */
  rate?: number;
  /**
   * 收款账户ID
   */
  receivedAccountId?: string;
  /**
   * 收款账户名称
   */
  receivedAccountName?: string;
  /**
   * 收款图片
   */
  receivePic?: string;
  /**
   * 收款备注
   */
  remark?: string;
  /**
   * 收款单号
   */
  serialNumber?: string;
  /**
   * 收款状态@seecom.ipms.finance.account.api.dto.enums.ReceivedStatusEnums
   */
  status?: ReceivedStatus;
  /**
   * 门店id
   */
  storeId?: string;
  /**
   * 门店名称
   */
  storeName?: string;
  /**
   * 收款金额，单位：分
   */
  totalReceivedAmount?: number;
  /**
   * 收款金额，单位：元
   */
  totalReceivedAmountYuan?: number;
  /**
   * 核销金额(带币种)
   */
  writeOffAmount?: string;
}