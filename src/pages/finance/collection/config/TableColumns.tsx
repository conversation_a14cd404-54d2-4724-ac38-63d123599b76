import { queryStoreByAccount } from '@/pages/system/user/services';
import { OverdueStatusValueEnum } from '@/types/CommonStatus';
import type { ProColumns } from '@ant-design/pro-components';
import type { IntlShape } from '@umijs/max';
import { type FinReceivableEntity } from '../types/FinReceivableEntity.entity';

export const getTableColumns = (intl: IntlShape): ProColumns<FinReceivableEntity>[] => [
  {
    title: intl.formatMessage({ id: 'common.column.index' }),
    valueType: 'index',
    width: 40,
    fixed: 'left',
  },
  {
    title: intl.formatMessage({ id: 'finance.collection.columns.customer' }),
    dataIndex: 'buyerName',
    width: 120,
  },
  {
    title: intl.formatMessage({ id: 'finance.collection.columns.store' }),
    dataIndex: 'storeName',
    width: 120,
    hideInSearch: true,
  },
  {
    title: intl.formatMessage({ id: 'finance.collection.columns.store' }),
    dataIndex: 'storeIdList',
    hideInTable: true,
    valueType: 'select',
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 3,
      showSearch: true,
    },
    formItemProps: {
      name: 'storeIdList',
    },
    request: async () => {
      const data = await queryStoreByAccount({});
      return data?.map(({ id, name }) => ({
        value: id,
        label: name,
      }));
    },
  },
  {
    title: intl.formatMessage({ id: 'finance.collection.columns.overdueStatus' }),
    dataIndex: 'overdueFlag',
    width: 120,
    fixed: 'left',
    valueType: 'select',
    valueEnum: OverdueStatusValueEnum,
    hideInTable: true,
  },
  {
    title: intl.formatMessage({ id: 'finance.collection.columns.receivableAmount' }),
    dataIndex: 'receivableAmountYuan',
    valueType: 'money',
    width: 100,
    search: false,
  },
  {
    title: intl.formatMessage({ id: 'finance.collection.columns.overdueAmount' }),
    dataIndex: 'overdueAmountYuan',
    valueType: 'money',
    width: 100,
    search: false,
  },
  {
    title: intl.formatMessage({ id: 'finance.collection.columns.totalAmount' }),
    dataIndex: 'totalAmountYuan',
    // valueType: 'money',
    width: 100,
    search: false,
  },
  {
    title: intl.formatMessage({ id: 'finance.collection.columns.availableAmount' }),
    dataIndex: 'availableAmountYuan',
    // valueType: 'money',
    width: 100,
    search: false,
  },
  {
    title: intl.formatMessage({ id: 'finance.collection.columns.settleType' }),
    dataIndex: 'creditTermsType',
    width: 120,
    search: false,
  },
  {
    title: intl.formatMessage({ id: 'finance.collection.columns.overdueStatus' }),
    dataIndex: 'status',
    width: 120,
    valueType: 'select',
    search: false,
    valueEnum: OverdueStatusValueEnum,
  },
];
