export enum DeliveryState {
    /** 待领取 */
    PENDING = 1,
    /** 已领取 */
    PICKED_UP = 2,
    /** 配送中 */
    IN_DELIVERY = 3,
    /** 配送完成 */
    COMPLETED = 4,
    /** 已取消 */
    CANCELLED = 5,
}

/**
 * Delivery type enum
 */
export enum DeliveryType {
    /** 送货 */
    DELIVERY = 1,
    /** 取货 */
    PICKUP = 2,
}

/**
 * Distribution mode enum
 */
export enum DistributionMode {
    /** 客户自提 */
    SELF_PICKUP = 1,
    /** 商家配送 */
    MERCHANT_DELIVERY = 2,
    /** 快递物流 */
    LOGISTICS = 3,
}



export enum TargetType {
    /**
     * 供应商
     */
    SUPPLIER = 1,
    /**
     * 客户
     */
    CUSTOMER = 2,
}
