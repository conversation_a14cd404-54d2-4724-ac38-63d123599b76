
import FunProTable from '@/components/common/FunProTable';
import withKeep<PERSON>live from '@/wrappers/withKeepAlive';
import { PageContainer } from '@ant-design/pro-components';
import { ActionType } from '@ant-design/pro-table';
import { useIntl } from '@umijs/max';
import { Button, message } from 'antd';
import { useRef, useState } from 'react';
import { finishDelivery, queryDeliveryPage, startDelivery } from '../services';
import BatchAssignModal from './components/BatchAssignModal';
import CreateTaskModal from './components/CreateTaskModal';
import { deliveryListColumns } from './config/deliveryListColumns';
import { DeliveryListItem, DeliveryListSearchParams } from './type';

const Page = () => {
    const intl = useIntl();
    const t = (id: string, ...rest) => intl.formatMessage({ id }, ...rest);
    const actionRef = useRef<ActionType>();

    const [selectedRows, setSelectedRows] = useState<DeliveryListItem[]>([]);
    const [batchAssignModalVisible, setBatchAssignModalVisible] = useState(false);
    const [createTaskModalVisible, setCreateTaskModalVisible] = useState(false);

    const handleStartDelivery = async (record: DeliveryListItem) => {
        await startDelivery({ id: record.id });
        message.success(t('stocks.delivery.list.message.startDeliverySuccess'));
        actionRef.current?.reload();
    };

    const handleFinishDelivery = async (record: DeliveryListItem) => {
        await finishDelivery({ id: record.id });
        message.success(t('stocks.delivery.list.message.finishDeliverySuccess'));
        actionRef.current?.reload();
    };

    const handleAssign = (record: DeliveryListItem) => {
        // For single assignment, we can reuse the batch assign modal
        setSelectedRows([record]);
        setBatchAssignModalVisible(true);
    };

    const columns = deliveryListColumns(handleAssign, handleStartDelivery, handleFinishDelivery);


    return (
        <PageContainer>
            <FunProTable<DeliveryListItem, DeliveryListSearchParams>
                rowKey="id"
                actionRef={actionRef}
                requestPage={queryDeliveryPage}
                scroll={{ x: 'max-content' }}
                columns={columns}
                rowSelection={{
                    onChange: (_, selectedRows) => {
                        setSelectedRows(selectedRows);
                    },
                }}
                toolBarRender={() => [
                    <Button
                        type="primary"
                        key="createTask"
                        onClick={() => setCreateTaskModalVisible(true)}
                    >
                        {t('stocks.delivery.list.button.createTask')}
                    </Button>,
                    <Button
                        type="primary"
                        key="batchAssign"
                        disabled={selectedRows.length === 0}
                        onClick={() => setBatchAssignModalVisible(true)}
                    >
                        {t('stocks.delivery.list.button.batchAssign')}
                    </Button>,
                ]}
            />
            <BatchAssignModal
                visible={batchAssignModalVisible}
                onClose={() => setBatchAssignModalVisible(false)}
                selectedRows={selectedRows}
                onSuccess={() => actionRef.current?.reload()}
            />
            <CreateTaskModal
                visible={createTaskModalVisible}
                onClose={() => setCreateTaskModalVisible(false)}
                onSuccess={() => actionRef.current?.reload()}
            />
        </PageContainer>
    );
};

export default withKeepAlive(Page);
