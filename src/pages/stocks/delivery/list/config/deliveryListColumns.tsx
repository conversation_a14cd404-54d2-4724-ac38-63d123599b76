
import Tag from '@/pages/finance/tag';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { accountListQuerySimple } from '@/pages/system/user/services';
import { ProColumns } from '@ant-design/pro-components';
import { FormattedMessage, useIntl } from '@umijs/max';
import { Space } from 'antd';
import { DeliveryEntity } from '../../types/Delivery.entity';
import { DeliveryState, DistributionMode } from '../../types/delivery.enums';
import { DeliveryListItem } from './type';

export const deliveryListColumns = (
  handleAssign: (record: DeliveryListItem) => void,
  handleStartDelivery: (record: DeliveryListItem) => void,
  handleFinishDelivery: (record: DeliveryListItem) => void,
): ProColumns<DeliveryEntity>[] => {
  const intl = useIntl();
  const t = (id: string, ...rest) => intl.formatMessage({ id }, ...rest);

  return [
    {
      title: t('common.column.index'),
      dataIndex: 'index',
      valueType: 'index',
      width: 60,
    },
    {
      title: t('stocks.delivery.list.column.deliveryNo'),
      dataIndex: 'bizBillNo',
      width: 180,
      order: 1,
      render: (text, record) => {
        return (
          <Space>
            <span>{text}</span>
            {Boolean(record.isUrgent) && <Tag color="red">{t('delivery.tag.urgent')}</Tag>}
          </Space>
        );
      },
    },
    {
      title: t('stocks.delivery.list.column.businessNo'),
      dataIndex: 'origBillNo',
      width: 150,
      order: 2
    },
    {
      title: t('stocks.delivery.list.column.taskType'),
      dataIndex: 'billTypeDesc',
      width: 100,
      hideInSearch: true,
    },
    {
      title: t('stocks.delivery.list.column.warehouse'),
      dataIndex: 'warehouseName',
      width: 120,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 'responsive',
        showSearch: true,
      },
      formItemProps: {
        name: 'warehosueIdList',
      },
      request: async () => {
        const data = await warehouseList({});
        return data?.warehouseSimpleRoList?.map(({ id, warehouseName }) => ({
          value: id,
          label: warehouseName,
        }));
      },
      order: 10
    },
    {
      title: t('stocks.delivery.list.column.status'),
      dataIndex: 'state',
      width: 100,
      valueType: 'select',
      valueEnum: {
        [DeliveryState.PENDING]: { text: <FormattedMessage id="stocks.delivery.list.state.pending" />, status: 'Default' },
        [DeliveryState.PICKED_UP]: { text: <FormattedMessage id="stocks.delivery.list.state.pickedUp" />, status: 'Processing' },
        [DeliveryState.IN_DELIVERY]: { text: <FormattedMessage id="stocks.delivery.list.state.inDelivery" />, status: 'Processing' },
        [DeliveryState.COMPLETED]: { text: <FormattedMessage id="stocks.delivery.list.state.completed" />, status: 'Success' },
        [DeliveryState.CANCELLED]: { text: <FormattedMessage id="stocks.delivery.list.state.cancelled" />, status: 'Error' },
      },
      order: 9
    },
    {
      title: t('stocks.delivery.list.column.createTime'),
      dataIndex: 'createTime',
      width: 150,
      hideInSearch: true,
    },
    {
      title: t('stocks.delivery.list.column.createTime'),
      dataIndex: 'createTime',
      width: 150,
      valueType: 'dateTimeRange',
      search: {
        transform: (value: any) => {
          return {
            startCreateTime: value[0],
            endCreateTime: value[1],
          };
        },
      },
      hideInTable: true,
    },
    {
      title: t('stocks.delivery.list.column.expectedArrivalTime'),
      dataIndex: 'expectedArrTime',
      width: 150,
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: t('stocks.delivery.list.column.deliveryMan'),
      dataIndex: 'deliveryManId',
      width: 120,
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'name', value: 'id' },
      },
      request: () => accountListQuerySimple({}),
      order: 8
    },
    {
      title: t('stocks.delivery.list.column.contact'),
      dataIndex: 'contactName',
      order: 6
    },
    {
      title: t('stocks.delivery.list.column.contactAddress'),
      dataIndex: 'deliveryAddress',
      width: 200,
      hideInSearch: true,
    },
    {
      title: t('stocks.delivery.list.column.remark'),
      dataIndex: 'remark',
      hideInSearch: true,
    },
    {
      title: t('stocks.delivery.list.column.deliveryMethod'),
      dataIndex: 'distributionMode',
      hideInSearch: true,
      valueType: 'select',
      valueEnum: {
        [DistributionMode.SELF_PICKUP]: { text: <FormattedMessage id="stocks.delivery.list.distributionMode.selfPickup" /> },
        [DistributionMode.MERCHANT_DELIVERY]: { text: <FormattedMessage id="stocks.delivery.list.distributionMode.merchantDelivery" /> },
        [DistributionMode.LOGISTICS]: { text: <FormattedMessage id="stocks.delivery.list.distributionMode.logistics" /> },
      },
    },

    {
      title: t('stocks.delivery.list.column.deliveryTarget'),
      dataIndex: 'deliveryTargetId',
      hideInTable: true,
      fieldProps: {
        showSearch: true,
      },
      order: 7
    },
    {
      title: t('stocks.delivery.list.column.deliveryTarget'),
      dataIndex: 'deliveryTargetName',
      width: 120,
      hideInSearch: true,
    },
    {
      title: t('stocks.delivery.list.column.startTime'),
      dataIndex: 'beginTime',
      width: 150,
      hideInSearch: true,
    },
    {
      title: t('stocks.delivery.list.column.finishTime'),
      dataIndex: 'finishTime',
      width: 150,
      hideInSearch: true,
    },
    {
      title: t('common.column.operation'),
      key: 'operation',
      width: 200,
      fixed: 'right',
      hideInSearch: true,
      render: (_, record) => {
        return (
          <Space>
            {record.state === DeliveryState.PENDING && (
              <a onClick={() => handleAssign(record)}>
                {t('stocks.delivery.list.action.assign')}
              </a>
            )}
            {record.state === DeliveryState.PICKED_UP && (
              <a onClick={() => handleStartDelivery(record)}>
                {t('stocks.delivery.list.action.start')}
              </a>
            )}
            {record.state === DeliveryState.IN_DELIVERY && (
              <a onClick={() => handleFinishDelivery(record)}>
                {t('stocks.delivery.list.action.finish')}
              </a>
            )}
            <a onClick={() => handleFinishDelivery(record)}>
              {t('common.button.edit')}
            </a>
            <a onClick={() => handleFinishDelivery(record)}>
              {t('common.button.cancel')}
            </a>
          </Space>
        );
      },
    },
  ];
};
