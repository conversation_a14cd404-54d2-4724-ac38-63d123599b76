
import ProFormObject, { ObjectType } from '@/components/ProFormItem/ProFormObject';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { accountListQuerySimple } from '@/pages/system/user/services';
import { ModalForm, ProFormDateTimePicker, ProFormRadio, ProFormSelect, ProFormText, ProFormTextArea } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Flex, Form } from 'antd';
import { createDelivery } from '../../services';
import { DeliveryEntity } from '../../types/delivery.entity';
import { DeliveryType, DistributionMode } from '../../types/delivery.enums';

interface CreateTaskModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const CreateTaskModal: React.FC<CreateTaskModalProps> = ({ visible, onClose, onSuccess }) => {
  const intl = useIntl();
  const t = (id: string, ...rest) => intl.formatMessage({ id }, ...rest);
  const [form] = Form.useForm<DeliveryEntity>();

  const handleFinish = async (values: DeliveryEntity) => {

    await createDelivery({
      ...values,
      deliveryTargetType: values.deliveryTargetType === ObjectType.Suppler ? 1 : 2
    });
    onSuccess();
    onClose();
    return true;
  };

  return (
    <ModalForm<DeliveryEntity>
      title={t('stocks.delivery.list.button.createTask')}
      width={800}
      form={form}
      open={visible}
      onOpenChange={(visible) => {
        if (!visible) {
          onClose();
        }
      }}
      onFinish={handleFinish}
      initialValues={{
        billType: DeliveryType.DELIVERY,
        distributionMode: DistributionMode.SELF_PICKUP,
      }}
      submitter={{
        render: (_, dom) => <Flex justify="center" className='w-full' gap={20}>{dom}</Flex>,
      }}
    >
      <ProFormRadio.Group
        name="billType"
        label={t('stocks.delivery.list.column.taskType')}
        options={[
          { label: t('stocks.delivery.list.billType.delivery'), value: DeliveryType.DELIVERY },
          { label: t('stocks.delivery.list.billType.pickup'), value: DeliveryType.PICKUP },
        ]}
      />
      <ProFormSelect
        name="warehouseId"
        label={t('stocks.delivery.list.column.store')}
        showSearch
        request={async () => {
          const data = await warehouseList({});
          return data?.warehouseSimpleRoList?.map(({ id, warehouseName }) => ({
            value: id,
            label: warehouseName,
          }));
        }}
        onChange={(value, option) => {
          form.setFieldsValue({
            warehouseName: option?.label,
          });
        }}
      />
      <ProFormText name="warehouseName" hidden />
      <ProFormObject
        objects={[ObjectType.Customer, ObjectType.Suppler]}
        label={t('stocks.delivery.list.column.deliveryTarget')}
        form={form}
        fieldsName={{
          fieldType: 'deliveryTargetType',
          fieldName: 'deliveryTargetName',
          fieldId: 'deliveryTargetId',
        }}
      />
      <ProFormText name="origBillNo" label={t('stocks.delivery.list.column.businessNo')} />

      <ProFormSelect
        name="deliveryManId"
        label={t('stocks.delivery.list.column.deliveryMan')}
        showSearch
        request={() => {
          return accountListQuerySimple({}).then((data) => {
            console.log(data);
            return data?.map(({ id, name }) => ({
              value: id,
              label: name,
            }));
          });
        }}
      />
      <ProFormText name="deliveryMan" hidden />
      <ProFormDateTimePicker name="expectedArrTime" label={t('stocks.delivery.list.column.expectedArrivalTime')} />
      <ProFormTextArea name="remark" label={t('stocks.delivery.list.column.description')} rules={[{ required: true, }]} />
    </ModalForm>
  );
};

export default CreateTaskModal;

