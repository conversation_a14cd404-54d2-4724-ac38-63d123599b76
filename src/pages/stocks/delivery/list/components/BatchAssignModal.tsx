
import { accountListQuerySimple } from '@/pages/system/user/services';
import { useIntl } from '@umijs/max';
import { Form, Modal, Select, message } from 'antd';
import { useState } from 'react';
import { batchAssign } from '../../services';
import { BatchAssignModalProps } from '../type';

const BatchAssignModal = ({ visible, onClose, selectedRows, onSuccess }: BatchAssignModalProps) => {
    const intl = useIntl();
    const t = (id: string, ...rest) => intl.formatMessage({ id }, ...rest);

    const [form] = Form.useForm();
    const [selectedDeliveryMan, setSelectedDeliveryMan] = useState<{ value: string; label: string }>();

    const handleOk = async () => {
        try {
            const values = await form.validateFields();
            await batchAssign({
                deliveryManId: values.deliveryManId,
                deliveryMan: selectedDeliveryMan?.label || '',
                idList: selectedRows.map((row) => row.id),
            });
            message.success(t('stocks.delivery.list.message.assignSuccess'));
            onSuccess?.();
            onClose();
        } catch (error) {
            console.log(error);
        }
    };

    return (
        <Modal
            title={t('stocks.delivery.list.action.assign')}
            visible={visible}
            onOk={handleOk}
            onCancel={onClose}
        >
            <Form form={form} layout="vertical">
                <Form.Item
                    name="deliveryManId"
                    label={t('stocks.delivery.list.column.deliveryMan')}
                    rules={[{ required: true }]}
                >
                    <Select
                        onChange={(_, option) => setSelectedDeliveryMan(option as any)}
                        showSearch
                        request={async () => {
                            const data = await accountListQuerySimple({});
                            console.log(data);
                            return data.map((item) => ({ value: item.id, label: item.name }));
                        }}
                    />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default BatchAssignModal;
