import { PageRequestParamsType } from "@/types/PageRequestParamsType";
import { PageResponseDataType } from "@/types/PageResponseDataType";
import { request } from "@/utils/request";
import { DeliveryEntity } from "./types/Delivery.entity";


/**
 * 运单列表
 */
export const queryDeliveryPage = async (params: Partial<DeliveryEntity> & PageRequestParamsType): Promise<PageResponseDataType<DeliveryEntity>> => {
    return request(`/ipmswarehouse/DeliveryFacade/queryByPage`, {
        data: params,
    });
};


/**
 * 查询运单详情
 */
export const queryDeliveryDetail = async (params: { id: string }): Promise<DeliveryEntity> => {
    return request(`/ipmswarehouse/DeliveryFacade/queryDetail`, {
        data: params,
    });
};


/**
 * 新增运单任务
 */
export const createDelivery = async (params: Partial<DeliveryEntity>): Promise<boolean> => {
    return request(`/ipmswarehouse/DeliveryFacade/insert`, {
        data: params,
    });
};

/**
 * 开始配送
 */
export const startDelivery = async (params: { id: string }): Promise<boolean> => {
    return request(`/ipmswarehouse/DeliveryFacade/startDelivery`, {
        data: params,
    });
};

/**
 * 完成配送
 */
export const finishDelivery = async (params: { id: string }): Promise<boolean> => {
    return request(`/ipmswarehouse/DeliveryFacade/finishDelivery`, {
        data: params,
    });
};


/**
 * 批量分配配送员
 */
export const batchAssign = async (params: {
    deliveryManId: string;
    deliveryMan: string;
    idList: string[]; //运单主键id列表
}): Promise<boolean> => {
    return request(`/apigateway/ipmswarehouse/DeliveryFacade/batchAssign`, {
        data: params,
    });
};

// /ipmswarehouse/DeliveryFacade/uploadImages
export const uploadImages = async (params: {
    id: string;
    images: string[];
}): Promise<boolean> => {
    return request(`/ipmswarehouse/DeliveryFacade/uploadImages`, {
        data: params,
    });
};
