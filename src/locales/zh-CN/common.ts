export default {
    'column.index': '序号',
    'column.operation': '操作',
    'column.createTime': '添加时间',
    'column.updateTime': '更新时间',
    'column.operationTime': '操作时间',
    'column.remark': '备注',
    'column.status': '状态',
    'unit.yuan': '元',
    'unit.day': '天',
    'option.yes': '是',
    'option.no': '否',
    'option.enable': '启用',
    'option.disable': '禁用',
    'option.peddingAudit': '待审核',
    'button.add': '新增',
    'button.edit': '编辑',
    'button.save': '保存',
    'button.confirm': '确认',
    'button.audit': '审核',
    'button.receive': '收款',
    'button.cancel': '取消',
    'button.close': '关闭',
    'button.export': '导出',
    'button.download': '下载',
    'button.batchImport': '批量导入',
    'button.downloadTemplate': '下载模板',
    'button.delete': '删除',
    'button.enable': '启用',
    'button.disable': '禁用',
    'button.preview': '预览',
    'button.print': '打印',
    'button.copy': '复制',
    'button.view': '查看',
    'placeholder.input': '请输入',
    'placeholder.select': '请选择',
    'placeholder.inputWithMaxLength': '请输入，最多{maxLength}个字符',
    'label.remark': '备注',
    'confirm.delete': '确认删除吗',
    'noData': '暂无数据',

    'field.brand': '品牌',
    'field.category': '品类',

    'message.needSelectOne': '请至少选择一项',
    'message.deleteSuccess': '删除成功',
    'message.addSuccess': '添加成功',
    'message.submitSuccess': '提交成功',
    'message.operationSuccess': '操作成功',
    'message.editableTable.inputError': '请检查表单数据，存在必填项未填写或数据格式错误',
    'message.save.success': '保存成功',
    'message.operation.success': '操作成功',

    // 优惠类型
    'discount.type.none': '无优惠',
    'discount.type.wholeOrderDiscount': '整单打折',
    'discount.type.wholeOrderReduction': '整单减',

    // 付款方式
    'payment.method.account': '挂账',
    'payment.method.cash': '现款',

    // 配送方式
    'delivery.method.express': '快递',

    // 查询类型
    'search.type.exact': '精确查找',
    'search.type.inStockOnly': '仅看有货',

    // 逾期状态
    'overdue.status.normal': '未逾期',
    'overdue.status.overdue': '已逾期',
    'overdue.status.disabled': '停用',

    // 额度变更日志类型
    'credit.log.type.addAccount': '新增额度账户',
    'credit.log.type.modifyAccount': '变更额度账户',
    'credit.log.type.enable': '启用',
    'credit.log.type.lock': '锁定',
    'credit.log.type.cancel': '注销',

    // 财务属性来源
    'finance.tag.source.system': '系统预置',
    'finance.tag.source.custom': '自建',

    // 财务属性收支方向
    'finance.ledger.type.income': '收入',
    'finance.ledger.type.expense': '支出',

    // StocksInfoDrawer component
    'stocksInfoDrawer.title': '本地库存',
    'stocksInfoDrawer.totalInventory': '总库存',
    'stocksInfoDrawer.occupiedInventory': '占用库存',
    'stocksInfoDrawer.availableInventory': '可用库存',
    'stocksInfoDrawer.stockDistribution': '库存分布',
    'stocksInfoDrawer.warehouseName': '仓库名称',
    'stocksInfoDrawer.totalStock': '总库存',
    'stocksInfoDrawer.occupiedStock': '占用库存',
    'stocksInfoDrawer.availableStock': '可用库存',
    'stocksInfoDrawer.purchaseInTransit': '采购在途',
    'stocksInfoDrawer.upperLimit': '库存上限',
    'stocksInfoDrawer.lowerLimit': '库存下限',
    'stocksInfoDrawer.selected': '已选',

    // PriceInfoDrawer component
    'priceInfoDrawer.priceInfo': '价格信息',
    'priceInfoDrawer.purchaseHistory': '采购历史',
    'priceInfoDrawer.goodsPrice': '商品价格',
    'priceInfoDrawer.salePrice': '销售价',
    'priceInfoDrawer.lowestPrice': '最低价',
    'priceInfoDrawer.costPrice': '成本价',
    'priceInfoDrawer.customerLastFivePrices': '客户最近5次售价',
    'priceInfoDrawer.storeLastFivePrices': '门店最近5次售价',
    'priceInfoDrawer.lastTenPurchases': '近10次采购历史',
    'priceInfoDrawer.orderNo': '销售单号',
    'priceInfoDrawer.price': '售价',
    'priceInfoDrawer.quantity': '销售数量',
    'priceInfoDrawer.time': '时间',
    'priceInfoDrawer.customerName': '客户名称',
    'priceInfoDrawer.purchaseOrderNo': '采购单号',
    'priceInfoDrawer.supplier': '供应商',
    'priceInfoDrawer.purchaseQuantity': '采购数量',
    'priceInfoDrawer.purchasePrice': '采购价格',
    'priceInfoDrawer.purchaseTime': '采购时间',

    // Export utility
    'export.title': '提示',
    'export.content': '导出任务已经创建成功，是否查看导出结果？',
    'export.okText': '前往查看',

    // Import utility
    'import.title': '批量导入',
    'import.description': '请根据提供的模板进行数据导入',
    'import.downloadTemplate': '下载模板',
    'import.importFile': '导入文件',
    'import.processingTitle': '提示',
    'import.processingContent': '正在导入中，请稍等',
    'import.successTitle': '提示',
    'import.successContent': '导入任务已经创建成功，是否查看导入结果？',
    'import.successOkText': '前往查看',
    'import.successMessage': '导入成功',

    "confirm.title": "确认",
};
